import json
import time
from datetime import datetime

class ValuationSystem:
    def __init__(self):
        # Base depreciation rates (per day)
        self.depreciation_rates = {
            "car": 0.002,      # 0.2% per day
            "engine": 0.001,   # 0.1% per day
            "turbo": 0.0015,   # 0.15% per day
            "intercooler": 0.0008,  # 0.08% per day
            "ecu": 0.0005      # 0.05% per day (software depreciates slowly)
        }
        
        # Usage depreciation (per race)
        self.usage_depreciation = {
            "car": 0.001,      # 0.1% per race
            "engine": 0.002,   # 0.2% per race
            "turbo": 0.0025,   # 0.25% per race
            "intercooler": 0.001,  # 0.1% per race
            "ecu": 0.0002      # 0.02% per race
        }
        
        # Performance multipliers (based on power-to-weight ratio)
        self.performance_multipliers = {
            "low": 0.8,        # < 0.3 P/W ratio
            "medium": 1.0,     # 0.3-0.8 P/W ratio
            "high": 1.2,       # 0.8-1.5 P/W ratio
            "extreme": 1.5     # > 1.5 P/W ratio
        }
        
        # Condition multipliers
        self.condition_multipliers = {
            "excellent": 1.0,   # 90-100% condition
            "good": 0.85,       # 70-89% condition
            "fair": 0.65,       # 50-69% condition
            "poor": 0.4,        # 30-49% condition
            "broken": 0.1       # < 30% condition
        }
    
    def calculate_car_value(self, car_data, usage_data=None):
        """Calculate current value of a car including all parts"""
        if usage_data is None:
            usage_data = self.get_default_usage_data()
        
        # Base car value
        base_car_value = car_data.get("value", 1000)
        
        # Calculate car depreciation
        car_condition = self.calculate_condition(
            usage_data.get("car_age_days", 0),
            usage_data.get("races_completed", 0),
            "car"
        )
        
        car_value = base_car_value * car_condition
        
        # Add parts value
        parts_value = 0
        parts = car_data.get("parts", {})
        
        for part_type, part_data in parts.items():
            if part_data is not None:
                part_value = self.calculate_part_value(
                    part_data, 
                    part_type, 
                    usage_data.get(f"{part_type}_age_days", 0),
                    usage_data.get("races_completed", 0)
                )
                parts_value += part_value
        
        # Calculate performance bonus
        performance_bonus = self.calculate_performance_bonus(car_data)
        
        total_value = (car_value + parts_value) * performance_bonus
        
        return {
            "total_value": int(total_value),
            "car_base_value": int(car_value),
            "parts_value": int(parts_value),
            "performance_bonus": performance_bonus,
            "car_condition": car_condition,
            "breakdown": self.get_value_breakdown(car_data, usage_data)
        }
    
    def calculate_part_value(self, part_data, part_type, age_days, races_completed):
        """Calculate current value of a single part"""
        base_value = part_data.get("value", 100)
        
        condition = self.calculate_condition(age_days, races_completed, part_type)
        
        return base_value * condition
    
    def calculate_condition(self, age_days, races_completed, item_type):
        """Calculate condition percentage based on age and usage with enhanced realism"""
        # Base depreciation rates
        base_time_rate = self.depreciation_rates.get(item_type, 0.001)
        base_usage_rate = self.usage_depreciation.get(item_type, 0.001)

        # Enhanced time-based depreciation with accelerating curve
        # Items depreciate faster as they age (exponential decay)
        time_factor = 1.0 + (age_days / 365) * 0.5  # 50% faster after 1 year
        time_depreciation = age_days * base_time_rate * time_factor

        # Enhanced usage-based depreciation with wear acceleration
        # Heavy usage causes accelerated wear
        if races_completed > 50:
            usage_factor = 1.0 + ((races_completed - 50) / 100) * 0.3  # 30% faster after 50 races
        else:
            usage_factor = 1.0

        usage_depreciation = races_completed * base_usage_rate * usage_factor

        # Environmental factors (simulated)
        # Assume some environmental wear over time
        environmental_factor = min(0.1, age_days / 3650)  # Max 10% over 10 years

        # Maintenance factor - well-maintained items last longer
        # This would be modified by maintenance system
        maintenance_factor = 1.0  # Default, can be reduced by maintenance

        # Total depreciation with realistic curves
        total_depreciation = (time_depreciation + usage_depreciation + environmental_factor) * maintenance_factor

        # Cap depreciation at 95% to prevent items becoming worthless
        total_depreciation = min(0.95, total_depreciation)

        # Condition percentage
        condition = 1.0 - total_depreciation

        return max(0.05, condition)  # Minimum 5% value (even broken items have scrap value)
    
    def calculate_performance_bonus(self, car_data):
        """Calculate performance bonus based on car's power-to-weight ratio"""
        # Calculate total horsepower
        engine = car_data.get("parts", {}).get("engine", {})
        base_hp = engine.get("horsepower", 100)
        
        # Calculate boost from other parts
        total_boost = 0
        parts = car_data.get("parts", {})
        
        for part_type in ["turbo", "intercooler", "ecu"]:
            part = parts.get(part_type)
            if part and "horsepower_boost_percentage" in part:
                total_boost += part["horsepower_boost_percentage"]
        
        total_hp = base_hp * (1 + total_boost / 100)
        
        # Calculate total weight
        base_weight = car_data.get("weight", 500)
        parts_weight = 0
        
        for part_type, part in parts.items():
            if part and "weight" in part:
                parts_weight += part["weight"]
        
        total_weight = base_weight + parts_weight
        
        # Calculate power-to-weight ratio
        power_to_weight = total_hp / total_weight if total_weight > 0 else 0
        
        # Determine performance category
        if power_to_weight < 0.3:
            return self.performance_multipliers["low"]
        elif power_to_weight < 0.8:
            return self.performance_multipliers["medium"]
        elif power_to_weight < 1.5:
            return self.performance_multipliers["high"]
        else:
            return self.performance_multipliers["extreme"]
    
    def get_condition_category(self, condition_percentage):
        """Get condition category from percentage"""
        if condition_percentage >= 0.9:
            return "excellent"
        elif condition_percentage >= 0.7:
            return "good"
        elif condition_percentage >= 0.5:
            return "fair"
        elif condition_percentage >= 0.3:
            return "poor"
        else:
            return "broken"
    
    def get_value_breakdown(self, car_data, usage_data):
        """Get detailed breakdown of value calculation"""
        breakdown = {
            "car": {
                "base_value": car_data.get("value", 1000),
                "condition": self.calculate_condition(
                    usage_data.get("car_age_days", 0),
                    usage_data.get("races_completed", 0),
                    "car"
                ),
                "current_value": 0
            },
            "parts": {}
        }
        
        # Calculate car current value
        breakdown["car"]["current_value"] = int(
            breakdown["car"]["base_value"] * breakdown["car"]["condition"]
        )
        
        # Calculate parts breakdown
        parts = car_data.get("parts", {})
        for part_type, part_data in parts.items():
            if part_data is not None:
                condition = self.calculate_condition(
                    usage_data.get(f"{part_type}_age_days", 0),
                    usage_data.get("races_completed", 0),
                    part_type
                )
                
                breakdown["parts"][part_type] = {
                    "base_value": part_data.get("value", 100),
                    "condition": condition,
                    "current_value": int(part_data.get("value", 100) * condition),
                    "condition_category": self.get_condition_category(condition)
                }
        
        return breakdown
    
    def get_default_usage_data(self):
        """Get default usage data for new items"""
        return {
            "car_age_days": 0,
            "races_completed": 0,
            "engine_age_days": 0,
            "turbo_age_days": 0,
            "intercooler_age_days": 0,
            "ecu_age_days": 0
        }
    
    def update_usage_data(self, usage_data, races_completed_increment=1):
        """Update usage data after races"""
        current_time = time.time()
        
        # Update races completed
        usage_data["races_completed"] = usage_data.get("races_completed", 0) + races_completed_increment
        
        # Update age if not set
        if "last_update" not in usage_data:
            usage_data["last_update"] = current_time
        
        # Calculate days passed since last update
        days_passed = (current_time - usage_data.get("last_update", current_time)) / (24 * 3600)
        
        # Update age for all components
        usage_data["car_age_days"] = usage_data.get("car_age_days", 0) + days_passed
        usage_data["engine_age_days"] = usage_data.get("engine_age_days", 0) + days_passed
        usage_data["turbo_age_days"] = usage_data.get("turbo_age_days", 0) + days_passed
        usage_data["intercooler_age_days"] = usage_data.get("intercooler_age_days", 0) + days_passed
        usage_data["ecu_age_days"] = usage_data.get("ecu_age_days", 0) + days_passed
        
        usage_data["last_update"] = current_time
        
        return usage_data
    
    def estimate_selling_price(self, car_data, usage_data=None):
        """Estimate selling price (typically 60-80% of current value)"""
        valuation = self.calculate_car_value(car_data, usage_data)
        
        # Selling price is typically 70% of current value
        selling_multiplier = 0.7
        
        # Adjust based on condition
        avg_condition = valuation["car_condition"]
        if avg_condition > 0.8:
            selling_multiplier = 0.75  # Better condition = better selling price
        elif avg_condition < 0.5:
            selling_multiplier = 0.6   # Poor condition = lower selling price
        
        selling_price = int(valuation["total_value"] * selling_multiplier)
        
        return {
            "selling_price": selling_price,
            "current_value": valuation["total_value"],
            "selling_percentage": int(selling_multiplier * 100)
        }

    def calculate_enhanced_performance(self, car_data, usage_data=None):
        """Calculate enhanced performance metrics for realistic car behavior"""
        if usage_data is None:
            usage_data = self.get_default_usage_data()

        # Get base car stats
        base_weight = car_data.get("weight", 500)
        parts = car_data.get("parts", {})

        # Calculate total horsepower with condition effects
        engine = parts.get("engine", {})
        base_hp = engine.get("horsepower", 100)

        # Apply engine condition to horsepower
        engine_condition = self.calculate_condition(
            usage_data.get("engine_age_days", 0),
            usage_data.get("races_completed", 0),
            "engine"
        )
        effective_hp = base_hp * (0.7 + 0.3 * engine_condition)  # Condition affects 30% of power

        # Calculate boost from other parts with condition effects
        total_boost = 0
        for part_type in ["turbo", "intercooler", "ecu"]:
            part = parts.get(part_type)
            if part and "horsepower_boost_percentage" in part:
                part_condition = self.calculate_condition(
                    usage_data.get(f"{part_type}_age_days", 0),
                    usage_data.get("races_completed", 0),
                    part_type
                )
                effective_boost = part["horsepower_boost_percentage"] * part_condition
                total_boost += effective_boost

        total_hp = effective_hp * (1 + total_boost / 100)

        # Calculate total weight
        parts_weight = 0
        for part_type, part in parts.items():
            if part and "weight" in part:
                parts_weight += part["weight"]

        total_weight = base_weight + parts_weight

        # Power-to-weight ratio (safe division)
        power_to_weight = total_hp / total_weight if total_weight > 0 else 0

        # Calculate performance characteristics
        acceleration = power_to_weight * 100  # 0-100 scale
        top_speed = power_to_weight * 200 + 100 if total_weight > 0 else 100  # Estimated top speed
        handling = max(10, 100 - (total_weight - 400) / 10)  # Lighter cars handle better

        # Reliability based on parts condition
        avg_condition = sum([
            self.calculate_condition(usage_data.get(f"{part_type}_age_days", 0),
                                   usage_data.get("races_completed", 0), part_type)
            for part_type in ["engine", "turbo", "intercooler", "ecu"]
        ]) / 4

        reliability = avg_condition * 100

        return {
            "total_horsepower": int(total_hp),
            "total_weight": int(total_weight),
            "power_to_weight_ratio": round(power_to_weight, 3),
            "acceleration": min(100, int(acceleration)),
            "top_speed": int(top_speed),
            "handling": min(100, int(handling)),
            "reliability": int(reliability),
            "performance_class": self.get_performance_class(power_to_weight),
            "condition_effects": {
                "engine_condition": engine_condition,
                "avg_parts_condition": avg_condition
            }
        }

    def get_performance_class(self, power_to_weight_ratio):
        """Determine performance class based on power-to-weight ratio"""
        if power_to_weight_ratio >= 1.5:
            return "Hypercar"
        elif power_to_weight_ratio >= 1.0:
            return "Supercar"
        elif power_to_weight_ratio >= 0.6:
            return "Sports Car"
        elif power_to_weight_ratio >= 0.4:
            return "Performance"
        elif power_to_weight_ratio >= 0.25:
            return "Standard"
        else:
            return "Economy"

# Global valuation system instance
valuation_system = ValuationSystem()
